using AutoMapper;
using Elastic.Apm.AspNetCore.DiagnosticListener;
using Elastic.Apm.DiagnosticSource;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Newtonsoft.Json;
using NSwag;
using NSwag.AspNetCore;
using NSwag.Generation.Processors.Security;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using UNI.Master.API.Attributes;
using UNI.Master.API.Authorization;
using UNI.Master.API.Extensions;
using UNI.Master.BLL.MapperProfile;
using UNI.Model;
using UNI.Utilities.Keycloak.Extensions;
using UNI.Utilities.Keycloak.Models;
using Elastic.Apm.Instrumentations.SqlClient;
using Serilog;
using Scalar.AspNetCore;

namespace UNI.Master.API
{
    /// <summary>
    /// Startup class
    /// </summary>
    public class Startup
    {
        private readonly IWebHostEnvironment _env;
        /// <summary>
        /// Startup constructor
        /// </summary>
        /// <param name="env"></param>
        /// <param name="configuration"></param>
        public Startup(IWebHostEnvironment env, IConfiguration configuration)
        {
            //Common.Utils.SetEnvironmentVariable(env.IsDevelopment());
            Configuration = configuration;
        }
        /// <summary>
        /// Configuration interface
        /// </summary>
        public IConfiguration Configuration { get; }

        /// <summary>
        /// This method gets called by the runtime. Use this method to add services to the container.
        /// </summary>
        /// <param name="services"></param>
        public void ConfigureServices(IServiceCollection services)
        {
            // services.AddElasticApm(
            //     new AspNetCoreDiagnosticSubscriber(),
            //     new HttpDiagnosticsSubscriber(),
            //     new SqlClientDiagnosticSubscriber());

            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                {
                    options.RequireHttpsMetadata = false;
                    //options.Authority = $"{Configuration["Jwt:ClientUrl"]}/auth/realms/{Configuration["Jwt:ClientRealm"]}";
                    options.Authority = Configuration["Jwt:Authority"];
                    options.Audience = Configuration["Jwt:ClientId"];
                    options.IncludeErrorDetails = true;
                    options.TokenValidationParameters = new TokenValidationParameters
                    {
                        ClockSkew = TimeSpan.Zero,
                        ValidateAudience = Convert.ToBoolean(Configuration["Jwt:ValidateAudience"]),
                        ValidAudiences = Configuration["Jwt:ValidAudiences"]?.Split(";", StringSplitOptions.RemoveEmptyEntries),
                        ValidateIssuer = Convert.ToBoolean(Configuration["Jwt:ValidateIssuer"]),
                        ValidIssuer = Configuration["Jwt:ValidIssuer"],
                        ValidateLifetime = true,
                        RequireExpirationTime = true,
                        ValidateIssuerSigningKey = true,
                        //IssuerSigningKey = new SymmetricSecurityKey(Encoding.ASCII.GetBytes("123456"))
                    };
                    options.Events = new JwtBearerEvents
                    {
                        OnTokenValidated = context =>
                        {
                            MapRealmRoles(context);
                            return Task.CompletedTask;
                        }
                    };
                });

            services.AddControllers(options =>
            {
                options.Filters.Add(typeof(AuthorizeAttribute));
            })
                .AddNewtonsoftJson();

            services.AddOpenApiDocument(c =>
            {
                c.Title = "Uni Master API";

                c.AddSecurity("oauth2", new OpenApiSecurityScheme
                {
                    Type = OpenApiSecuritySchemeType.OAuth2,
                    Flow = OpenApiOAuth2Flow.Implicit,
                    Flows = new OpenApiOAuthFlows
                    {
                        Implicit = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = Configuration["Jwt:Authority"] + "/protocol/openid-connect/auth",
                            TokenUrl = Configuration["Jwt:Authority"] + "/protocol/openid-connect/token",
                            Scopes = new Dictionary<string, string>
                                {
                                    { "openid", "openid" },
                                    { "profile", "profile" }
                                }
                        }
                    },
                    Scheme = "Bearer",
                    Name = "Authorization",
                    OpenIdConnectUrl = Configuration["Jwt:OpenIdConnectUrl"],
                    In = OpenApiSecurityApiKeyLocation.Header
                });
                c.OperationProcessors.Add(new OperationSecurityScopeProcessor("oauth2"));
            });

            services.RegisterServices(Configuration);

            var config = new MapperConfiguration(cfg => { cfg.AddProfile(new AutoMapperProfile()); });
            services.AddSingleton(_ => config.CreateMapper());
            var settings = Configuration.GetExternalService<Settings>("Keycloak");
            services.AddKeyCloakClient(o =>
            {
                o.BaseUrl = settings.BaseUrl;
                o.Realm = settings.Realm;
                o.Credentials = settings.Credentials;
            });
            services.Configure<AppSettings>(Configuration.GetSection("AppSettings"));
        }
        /// <summary>
        /// This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        /// </summary>
        /// <param name="app"></param>
        /// <param name="env"></param>
        /// <param name="logger"></param>
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILoggerFactory logger)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            app.UseHttpsRedirection();
            app.UseDefaultFiles();
            app.UseStaticFiles();

            //app.UseMvc();
            //app.UseForwardedHeaders(new ForwardedHeadersOptions
            //{
            //    ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            //});

            app.UseRouting();
            app.UseOpenApi(
                config =>
                {
                    config.PostProcess = (document, request) =>
                    {
                        if (env.IsDevelopment()) return;
                        document.Servers.Clear();
                        document.Servers.Add(new OpenApiServer
                            { Url = Configuration["AppSettings:Server:BaseUrl"] ?? "" });
                    };
                    //config.Path = "/openapi/{documentName}.json";
                });

            app.UseSwaggerUi(
                options =>
            {
                options.OAuth2Client = new OAuth2ClientSettings
                {
                    ClientId = Configuration["Jwt:ClientId"] ?? "swagger",
                    UsePkceWithAuthorizationCodeGrant = true,
                };
                options.EnableTryItOut = true;
            }
            );

            app.UseSerilogRequestLogging();
            //logger.AddFile(Configuration.GetSection("Logging"));

            app.UseCors(builder =>
                builder.AllowAnyOrigin()
                    .AllowAnyHeader()
                    .AllowAnyMethod());
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllerRoute("api", "api/v{version}/{controller}/{action}/{id?}");
                //endpoints.MapScalarApiReference(c =>
                //{
                //    c.Authentication = new ScalarAuthenticationOptions()
                //    {
                //        OAuth2 = new OAuth2Options()
                //        {
                //            ClientId = Configuration["Jwt:ClientId"] ?? "swagger",
                //            Scopes = new List<string>() { "openid", "profile" }
                //        }
                //    };
                //    ;
                //    c.OpenApiRoutePattern = "/swagger/v1/swagger.json";


                //});
            });

        }
        private static void MapRealmRoles(TokenValidatedContext context)
        {
            if (!(context?.Principal?.Identity is ClaimsIdentity claimsIdentity)) return;
            var realmAccess = context.Principal.Claims.FirstOrDefault(w => w.Type == "realm_access");
            if (realmAccess == null) return;
            var realmRole = JsonConvert.DeserializeObject<Role>(realmAccess.Value);
            foreach (var role in realmRole.Roles)
            {
                claimsIdentity.AddClaim(new Claim(ClaimTypes.Role, role));
            }
        }
    }
}
